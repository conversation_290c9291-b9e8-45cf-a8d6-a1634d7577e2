# 简单的DLL路径设置脚本 - 仅Windows平台和CLion IDE
# 用法: set_dll_path(target_name [FORCE_CLION])
# FORCE_CLION: 强制在任何IDE中都执行（可选参数）
function(set_dll_path target_name)
    # 解析可选参数
    set(options FORCE_CLION)
    cmake_parse_arguments(ARGS "${options}" "" "" ${ARGN})

    if(NOT WIN32)
        return()
    endif()

    # 检测IDE环境，只在CLion中执行（除非强制执行）
    set(is_clion FALSE)

    if(ARGS_FORCE_CLION)
        set(is_clion TRUE)
        message(STATUS "强制执行DLL路径设置...")
    else()
        # 方法1: 检查CMAKE_GENERATOR
        if(CMAKE_GENERATOR MATCHES "CodeBlocks")
            set(is_clion TRUE)
        endif()

        # 方法2: 检查环境变量
        if(DEFINED ENV{CLION_IDE} OR DEFINED ENV{JETBRAINS_IDE})
            set(is_clion TRUE)
        endif()

        # 方法3: 检查CLion特有的变量
        if(DEFINED CMAKE_EXPORT_COMPILE_COMMANDS AND CMAKE_EXPORT_COMPILE_COMMANDS)
            # CLion通常会设置这个变量为TRUE
            set(is_clion TRUE)
        endif()

        # 方法4: 检查是否排除Qt Creator
        # Qt Creator通常使用"Unix Makefiles"或"Ninja"生成器，且不设置CodeBlocks相关变量
        if(CMAKE_GENERATOR MATCHES "Unix Makefiles" AND NOT CMAKE_GENERATOR MATCHES "CodeBlocks")
            # 可能是Qt Creator，进一步检查
            if(DEFINED ENV{QTCREATOR_VERSION} OR DEFINED ENV{QTC_VERSION})
                message(STATUS "检测到Qt Creator，跳过DLL路径设置")
                return()
            endif()
        endif()

        # 如果不是CLion，直接返回
        if(NOT is_clion)
            message(STATUS "跳过DLL路径设置 - 当前IDE不是CLion")
            return()
        endif()

        message(STATUS "检测到CLion IDE，开始设置DLL路径...")
    endif()

    # 收集DLL路径
    set(dll_paths "")

    # Qt路径 - 多种方式检测
    # 方法1: 从Qt6_DIR推导
    if(DEFINED Qt6_DIR AND EXISTS "${Qt6_DIR}")
        get_filename_component(qt_root "${Qt6_DIR}/../../.." ABSOLUTE)
        set(qt_bin "${qt_root}/bin")
        if(EXISTS "${qt_bin}")
            list(APPEND dll_paths "${qt_bin}")
        endif()
    endif()

    # 方法2: 从Qt6::qmake目标获取
    if(TARGET Qt6::qmake)
        get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
        if(QT_QMAKE_EXECUTABLE AND EXISTS "${QT_QMAKE_EXECUTABLE}")
            get_filename_component(qt_bin_dir "${QT_QMAKE_EXECUTABLE}" DIRECTORY)
            if(EXISTS "${qt_bin_dir}")
                list(APPEND dll_paths "${qt_bin_dir}")
            endif()
        endif()
    endif()

    # 自动遍历第三方库目录查找DLL（跳过POCO，因为有专门的处理）
    file(GLOB thirdparty_dirs "${CMAKE_SOURCE_DIR}/Thirdparty/*")
    foreach(lib_dir ${thirdparty_dirs})
        if(IS_DIRECTORY ${lib_dir})
            # 跳过POCO目录，因为有专门的DLL复制函数处理
            get_filename_component(lib_name "${lib_dir}" NAME)
            if(lib_name STREQUAL "Poco")
                continue()
            endif()

            # 查找可能包含DLL的目录：bin, lib, lib/*, 根目录
            set(search_dirs
                "${lib_dir}/bin"
                "${lib_dir}/lib"
                "${lib_dir}"
            )

            # 查找lib下的子目录
            file(GLOB lib_subdirs "${lib_dir}/lib/*")
            foreach(subdir ${lib_subdirs})
                if(IS_DIRECTORY ${subdir})
                    list(APPEND search_dirs "${subdir}")
                endif()
            endforeach()

            # 在每个可能的目录中查找DLL
            foreach(search_dir ${search_dirs})
                if(IS_DIRECTORY ${search_dir})
                    file(GLOB dll_files "${search_dir}/*.dll")
                    if(dll_files)
                        list(APPEND dll_paths "${search_dir}")
                    endif()
                endif()
            endforeach()
        endif()
    endforeach()

    # vcpkg路径 - 支持多种检测方式
    if(DEFINED VCPKG_INSTALLED_DIR AND DEFINED VCPKG_TARGET_TRIPLET)
        if(CMAKE_BUILD_TYPE STREQUAL "Debug")
            set(vcpkg_bin "${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug/bin")
        else()
            set(vcpkg_bin "${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/bin")
        endif()
        if(EXISTS "${vcpkg_bin}")
            list(APPEND dll_paths "${vcpkg_bin}")
        endif()
    elseif(DEFINED CMAKE_TOOLCHAIN_FILE AND CMAKE_TOOLCHAIN_FILE MATCHES "vcpkg")
        # 尝试从构建目录推导vcpkg路径
        set(vcpkg_build_bin "${CMAKE_BINARY_DIR}/vcpkg_installed/x64-windows/bin")
        set(vcpkg_build_debug_bin "${CMAKE_BINARY_DIR}/vcpkg_installed/x64-windows/debug/bin")
        if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND EXISTS "${vcpkg_build_debug_bin}")
            list(APPEND dll_paths "${vcpkg_build_debug_bin}")
        elseif(EXISTS "${vcpkg_build_bin}")
            list(APPEND dll_paths "${vcpkg_build_bin}")
        endif()
    endif()

    # 去重并过滤空路径
    if(dll_paths)
        list(REMOVE_DUPLICATES dll_paths)
        # 过滤掉不存在的路径
        set(valid_dll_paths "")
        foreach(path ${dll_paths})
            if(EXISTS "${path}")
                list(APPEND valid_dll_paths "${path}")
            endif()
        endforeach()
        set(dll_paths ${valid_dll_paths})
    endif()

    # 如果有有效的DLL路径，设置环境变量
    if(dll_paths)
        # 将路径列表转换为Windows PATH格式（分号分隔）
        string(REPLACE ";" "\\;" path_string "${dll_paths}")

        # 为CLion设置运行时环境变量
        # CLion支持ENVIRONMENT属性来设置运行时环境变量
        set_target_properties(${target_name} PROPERTIES
            ENVIRONMENT "PATH=${path_string}\\;$ENV{PATH}"
        )

        # 同时设置VS调试环境（兼容性）
        set_target_properties(${target_name} PROPERTIES
            VS_DEBUGGER_ENVIRONMENT "PATH=${path_string};$ENV{PATH}"
        )
    endif()

    # 生成CLion环境变量文件
    if(dll_paths)
        # 将路径列表转换为Windows PATH格式（分号分隔）
        string(REPLACE ";" ";" path_string "${dll_paths}")

        # 生成CLion环境变量批处理文件
        set(clion_env_file "${CMAKE_BINARY_DIR}/clion_env_${target_name}.cmd")
        file(WRITE "${clion_env_file}" "@echo off\n")
        file(APPEND "${clion_env_file}" "set PATH=${path_string};%PATH%\n")

        # 输出结果
        message(STATUS "DLL环境变量已设置给目标 '${target_name}':")
        message(STATUS "新增的DLL路径:")
        foreach(path ${dll_paths})
            message(STATUS "  ${path}")
        endforeach()
        message(STATUS "")
        message(STATUS "CLion环境变量文件已生成: ${clion_env_file}")
        message(STATUS "在CLion中使用方法:")
        message(STATUS "  1. 打开 Run/Debug Configurations")
        message(STATUS "  2. 选择 Environment variables")
        message(STATUS "  3. 点击 'Load variables from file'")
        message(STATUS "  4. 选择文件: ${clion_env_file}")
        message(STATUS "")
    else()
        message(WARNING "未找到任何DLL路径给目标 '${target_name}'")
        message(STATUS "请检查:")
        message(STATUS "  - Qt6是否正确安装和配置")
        message(STATUS "  - 第三方库是否存在于 src/Thirdparty/ 目录")
        message(STATUS "  - vcpkg是否正确配置")
    endif()
endfunction()
