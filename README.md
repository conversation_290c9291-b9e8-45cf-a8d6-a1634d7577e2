# 编译sleuthkit

//

# 编译qtkeychain



```
mkdir build
cd build
```

release

```
cmake .. -DBUILD_WITH_QT6=ON -DBUILD_SHARED_LIBS=OFF -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

清理

debug

```
cmake .. -DBUILD_WITH_QT6=ON -DBUILD_SHARED_LIBS=OFF
cmake --build . 
```





# 集成 poco+sqlcipher 静态库

## sqlcipher和poco分开编译 （终极版）

## ~~sqlcipher和poco分开编译 （过程版）~~

参考： https://blog.2cn.in/index.php/archives/sqlcipher-msvc.html

### 编译sqlcipher库

clone sqlcipher的代码

```
git clone https://github.com/sqlcipher/sqlcipher
```

clone poco的代码

```
git clone https://github.com/pocoproject/poco
```

### tclsh

下载（我使用了9.0.1版本）： [Download Tcl/Tk Sources](https://www.tcl-lang.org/software/tcltk/download.html)

编译 tclsh：打开cmd

```
cd PATH/TO/TCLSH/SOURCE
cd win
nmake /f makefile.vc release

// assuming you'd like to install it to the default location
nmake /f makefile.vc INSTALLDIR=c:\Tcl install  

copy c:\Tcl\bin\tclsh86t.exe c:\Tcl\bin\tclsh.exe

// now recompile it statically
nmake /f makefile.vc OPTS=nothreads,static shell

// change the release folder accordingly
copy Release_AMD64_VC1937\tcl86s.lib c:\Tcl\lib 
set TCLDIR=c:\Tcl
set PATH=%PATH%;c:\tcl\bin
```

不要关闭这个<mark>cmd窗口</mark>，cd进sqlcipher项目根目录（先放着待会要用。。。。。。）

文件管理器打开sqlcipher\src\sqlcipher.c

然后在这个文件里搜索 uint64_t，然后都替换成 sqlite3_uint64，保存（这一步卡了我两天，没人说msvc编译要这么修改）

再回到刚刚的cmd窗口>>

release：编译msvc MD static x64的库，并且链接动态的openssl

```
nmake /f Makefile.msc libsqlite3.lib "PLATFORM=x64" "USE_CRT_DLL=1" "DEBUG=0" USE_NATIVE_LIBPATHS=1 "OPTS=-DSQLITE_HAS_CODEC -DSQLITE_TEMP_STORE=2 -DSQLITE_EXTRA_INIT=sqlcipher_extra_init -DSQLITE_EXTRA_SHUTDOWN=sqlcipher_extra_shutdown -DSQLITE_ENABLE_FTS3=1 -DSQLITE_ENABLE_FTS4=1 -DSQLITE_ENABLE_FTS5=1 -DSQLITE_ENABLE_RTREE=1 -DSQLITE_ENABLE_JSON1=1 -DSQLITE_ENABLE_GEOPOLY=1 -DSQLITE_ENABLE_SESSION=1 -DSQLITE_ENABLE_PREUPDATE_HOOK=1 -DSQLITE_ENABLE_SERIALIZE=1 -DSQLITE_ENABLE_MATH_FUNCTIONS=1 -ID:\Software\vcpkg\installed\x64-windows\include" LIBTCL="tcl90s.lib netapi32.lib user32.lib" LTLINKOPTS="-LIBPATH:D:\Software\vcpkg\installed\x64-windows\lib libcrypto.lib libssl.lib user32.lib Ws2_32.lib Advapi32.lib Crypt32.lib"
```

debug：编译msvc MD shared x64的库，并且链接动态的openssl

```
nmake /f Makefile.msc sqlite3.dll "PLATFORM=x64" "USE_CRT_DLL=1" "DEBUG=3" "DYNAMIC_SHELL=1" USE_NATIVE_LIBPATHS=1 "OPTS=-DSQLITE_HAS_CODEC -DSQLITE_TEMP_STORE=2 -DSQLITE_EXTRA_INIT=sqlcipher_extra_init -DSQLITE_EXTRA_SHUTDOWN=sqlcipher_extra_shutdown -DSQLITE_ENABLE_FTS3=1 -DSQLITE_ENABLE_FTS4=1 -DSQLITE_ENABLE_FTS5=1 -DSQLITE_ENABLE_RTREE=1 -DSQLITE_ENABLE_JSON1=1 -DSQLITE_ENABLE_GEOPOLY=1 -DSQLITE_ENABLE_SESSION=1 -DSQLITE_ENABLE_PREUPDATE_HOOK=1 -DSQLITE_ENABLE_SERIALIZE=1 -DSQLITE_ENABLE_MATH_FUNCTIONS=1 -ID:\Software\vcpkg\installed\x64-windows\include" LIBTCL="tcl90s.lib netapi32.lib user32.lib" LTLINKOPTS="-LIBPATH:D:\Software\vcpkg\installed\x64-windows\lib libcrypto.lib libssl.lib user32.lib Ws2_32.lib Advapi32.lib Crypt32.lib"
```

其中，`-ID:\Software\vcpkg\installed\x64-windows\include`和`D:\Software\vcpkg\installed\x64-windows\lib`前者是放openssl头文件的地址、后者是放openssl的lib的地址，这两个换成你自己安装的openssl相应的地址

### 独立编译poco::datasqlite

先修改poco项目根目录下的components文件：(注释除了Foundation和Data以外的模块)

```
Foundation
# CppUnit
# Encodings
# XML
# JSON
# Util
# Net
# Crypto
# NetSSL_OpenSSL
# NetSSL_Win
Data
# Data/SQLite
# Data/ODBC
# Data/MySQL
# Data/PostgreSQL
# Data/DataTest
# Zip
# PageCompiler
# PageCompiler/File2Page
# JWT
# PDF
# CppParser
# MongoDB
# Redis
# Prometheus
# ActiveRecord
# ActiveRecord/Compiler
# DNSSD
# DNSSD/Avahi
# DNSSD/Bonjour
# PocoDoc
# ProGen
# Trace
```

在poco项目根目录编译：（release和debug分开编译，不然两者生成的lib混在一起了）

release版

```
.\buildwin.cmd 170 build static_md release x64
```

debug版

```
.\buildwin.cmd 170 build shared debug x64
```

在Data/SQLite根目录新建一个cmakelist：

SQLCIPHER_INCLUDE_DIR和SQLCIPHER_LIB_DIR换成你的地址

```
cmake_minimum_required(VERSION 3.15.0)

project(PocoDataSQLiteExternal VERSION 1.14.2)

# 设置C++17标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# SQLCipher路径
set(SQLCIPHER_INCLUDE_DIR "D:/BWorkspace/TestFindGame/Thirdparty/Sqlcipher/Include")
set(SQLCIPHER_LIB_DIR "D:/BWorkspace/TestFindGame/Thirdparty/Sqlcipher/Debug/lib")

# 查找SQLCipher库
find_library(SQLCIPHER_LIBRARY
    NAMES sqlite3 sqlcipher
    PATHS ${SQLCIPHER_LIB_DIR}
    NO_DEFAULT_PATH
)

if(NOT SQLCIPHER_LIBRARY)
    message(FATAL_ERROR "SQLCipher library not found")
endif()

message(STATUS "SQLCipher library: ${SQLCIPHER_LIBRARY}")

# 源文件列表
set(SOURCES
    src/Binder.cpp
    src/Connector.cpp
    src/Extractor.cpp
    src/Notifier.cpp
    src/SessionImpl.cpp
    src/SQLiteException.cpp
    src/SQLiteStatementImpl.cpp
    src/Utility.cpp
)

# 创建动态库
add_library(PocoDataSQLiteExternal SHARED ${SOURCES})

# 设置目标属性
set_target_properties(PocoDataSQLiteExternal PROPERTIES
    OUTPUT_NAME "PocoDataSQLiteExternal"
    VERSION ${PROJECT_VERSION}
)

# 包含目录
target_include_directories(PocoDataSQLiteExternal PRIVATE
    include
    ../../Foundation/include
    ../../Data/include
    ${SQLCIPHER_INCLUDE_DIR}
)

# 编译定义
target_compile_definitions(PocoDataSQLiteExternal PRIVATE
    WIN32
    _WINDOWS
    _USRDLL
    SQLite_EXPORTS
    POCO_UNBUNDLED
    SQLITE_THREADSAFE=1
    SQLITE_ENABLE_FTS5
    SQLITE_HAS_CODEC
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

# Windows特定设置
if(WIN32)
    target_compile_definitions(PocoDataSQLiteExternal PRIVATE
        _CRT_SECURE_NO_WARNINGS
        _SCL_SECURE_NO_WARNINGS
    )

    target_compile_options(PocoDataSQLiteExternal PRIVATE
        /wd4996 /wd4244 /wd4018
    )
endif()

# Poco库路径
set(POCO_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../lib64")

# 查找Poco库
find_library(POCO_FOUNDATION_LIBRARY
    NAMES PocoFoundationd PocoFoundation
    PATHS ${POCO_LIB_DIR}
    NO_DEFAULT_PATH
)

find_library(POCO_DATA_LIBRARY
    NAMES PocoDatad PocoData
    PATHS ${POCO_LIB_DIR}
    NO_DEFAULT_PATH
)

if(NOT POCO_FOUNDATION_LIBRARY)
    message(FATAL_ERROR "Poco Foundation library not found in ${POCO_LIB_DIR}")
endif()

if(NOT POCO_DATA_LIBRARY)
    message(FATAL_ERROR "Poco Data library not found in ${POCO_LIB_DIR}")
endif()

message(STATUS "Poco Foundation library: ${POCO_FOUNDATION_LIBRARY}")
message(STATUS "Poco Data library: ${POCO_DATA_LIBRARY}")

# 链接库
target_link_libraries(PocoDataSQLiteExternal PRIVATE
    ${POCO_FOUNDATION_LIBRARY}
    ${POCO_DATA_LIBRARY}
    ${SQLCIPHER_LIBRARY}
)

# Windows系统库
if(WIN32)
    target_link_libraries(PocoDataSQLiteExternal PRIVATE
        ws2_32 iphlpapi crypt32
    )
endif()

message(STATUS "Configuration complete")
message(STATUS "SQLCipher: ${SQLCIPHER_LIBRARY}")
message(STATUS "Output: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
```

然后

```
mkdir final_build
cd final_build
cmake -G "Visual Studio 17 2022" -A x64 ..
```

编译debug动态库：

```
cmake --build . --config Debug
```

编译release静态库：

```
cmake --build . --config Release
```

## ~~sqlcipher和poco分开编译（这踏马是没有加密的数据库）~~

#### 编译sqlcipher库

clone sqlcipher的代码

```
git clone https://github.com/sqlcipher/sqlcipher
```

clone poco的代码

```
git clone https://github.com/pocoproject/poco
```

安装tcl/tk

x64 Native Tools Command Prompt for VS 2022（vs的命令行工具，在win系统开始菜单找）进入 sqlcipher 目录根目录

设置openssl环境变量（换成你的地址）

```
set OPENSSL_LIB_DIR=D:\Software\vcpkg\packages\openssl_x64-windows\lib
set OPENSSL_INC_DIR=D:\Software\vcpkg\packages\openssl_x64-windows\include\openssl
```

release：编译msvc MD static x64的库，并且链接动态的openssl

```
nmake /f Makefile.msc "PLATFORM=x64" "USE_CRT_DLL=1" "DEBUG=0" "CFLAGS=-DSQLITE_HAS_CODEC -DSQLITE_EXTRA_INIT=sqlcipher_extra_init -DSQLITE_EXTRA_SHUTDOWN=sqlcipher_extra_shutdown -I%OPENSSL_INC_DIR%" "LDFLAGS=/LIBPATH:%OPENSSL_LIB_DIR% libcrypto.lib" libsqlite3.lib
```

debug：编译msvc MD shared x64的库，并且链接动态的openssl

```
nmake /f Makefile.msc "PLATFORM=x64" "USE_CRT_DLL=1" "DEBUG=3" "MEMDEBUG=1" "DYNAMIC_SHELL=1" "CFLAGS=-DSQLITE_HAS_CODEC -DSQLITE_EXTRA_INIT=sqlcipher_extra_init -DSQLITE_EXTRA_SHUTDOWN=sqlcipher_extra_shutdown -I%OPENSSL_INC_DIR%" "LDFLAGS=/LIBPATH:%OPENSSL_LIB_DIR% libcrypto.lib" sqlite3.dll
```

#### 编译poco

release版

```
.\buildwin.cmd 170 build static_md release x64
```

debug版

```
.\buildwin.cmd 170 build shared debug x64
```

## ~~sqlcipher嵌入进poco编译（不能调用原生sqlite3 c api）~~

整合的灵感来源： https://github.com/tipi-build/adapter-poco-patch-sqlcipher 里的cmakelist

sqlcipher编译教程： https://github.com/sqlcipher/sqlcipher/blob/v4.9.0/doc/compile-for-windows.md

#### 编译sqlcipher文件头

clone sqlcipher的代码

```
git clone https://github.com/sqlcipher/sqlcipher
```

clone poco的代码

```
git clone https://github.com/pocoproject/poco
```

x64 Native Tools Command Prompt for VS 2022（vs的命令行工具，在win系统开始菜单找）进入 sqlcipher 目录根目录

以下指令来源： https://github.com/sqlcipher/sqlcipher

```
nmake /f Makefile.msc sqlite3.c "OPTS=-DSQLITE_HAS_CODEC -DSQLITE_TEMP_STORE=2 -DSQLITE_EXTRA_INIT=sqlcipher_extra_init -DSQLITE_EXTRA_SHUTDOWN=sqlcipher_extra_shutdown -DSQLCIPHER_CRYPTO_OPENSSL"
```

把编译出的sqlite3.c和sqlite3.h（在sqlcipher项目根目录）文件复制到poco以下目录

![](assets/2025-06-23-09-41-58-image.png)

#### 编译poco

修改根目录下的 components 文件：（把除了这三个组件的其他组件注释掉）

<img src="assets/2025-06-23-09-56-27-image.png" title="" alt="" width="319">

修改poco\Data\SQLite目录下的cmakelist：

![](assets/2025-06-24-09-48-09-image.png)

因为静态库太大了，所以采取release版静态链接，debug版动态链接

release版

```
.\buildwin.cmd 170 build static_md release x64
```

debug版

```
.\buildwin.cmd 170 build shared debug x64
```

编译的库按release和debug分好类

复制头文件，把include里的都复制出来（之所以知道从这些文件夹找，是因为对比过vcpkg下载的poco，我看了它的目录结构）

![](assets/2025-06-24-09-53-03-image.png)

![](assets/2025-06-24-09-54-14-image.png)

![](assets/2025-06-24-09-55-59-image.png)

然后有部分头文件是vcpkg下载poco时看到的，直接复制过来

![](assets/2025-06-24-10-00-51-image.png)

//todo 把sqlcipher的ssqlite3.h覆盖到poco的include里

## ~~单独集成poco（不用看这里，这个方法不能集成sqlcipher）~~

https://github.com/pocoproject/poco

使用 vcpkg安装poco

poco使用教程： https://docs.pocoproject.org/current/00200-DataUserManual.html

为什么不用 [GitHub - qicosmos/ormpp: modern C++ ORM, C++17, support mysql, postgresql,sqlite](https://github.com/qicosmos/ormpp)

- 优点
  
  - 基础的sql语句确实都不用写了
  
  - 比Poco::Tuple结合Poco::Data还更像Java的ORM（Java的很多东西确实先进且可靠）

- 致命缺点
  
  - star没poco多，换言之验证过的人不够多，即可能坑多
