// Copyright 2022 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef CRASHPAD_TEST_IOS_HANDLER_FORBIDDEN_ALLOCATIONS_H_
#define CRASHPAD_TEST_IOS_HANDLER_FORBIDDEN_ALLOCATIONS_H_

namespace crashpad {
namespace test {

// Override malloc_default_zone and malloc_default_purgeable_zone with functions
// that immediately exit if called from the same thread that this helper is
// called from or from the Crashpad Mach exception handler thread indicated by
// GetThreadIdForTesting. This is used to ensure the allocator is not used by
// the Crashpad InProcessHandler.
void ReplaceAllocatorsWithHandlerForbidden();

}  // namespace test
}  // namespace crashpad

#endif  // CRASHPAD_TEST_IOS_HANDLER_FORBIDDEN_ALLOCATIONS_H_
