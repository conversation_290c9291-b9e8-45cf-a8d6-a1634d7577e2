// Copyright 2014 The Crashpad Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#error This file is not intended to be #included.

//! \namespace crashpad
//! \brief The main namespace.

//! \namespace crashpad::internal
//! \brief The internal namespace, not for public use.

//! \namespace crashpad::test
//! \brief The testing namespace, for use in test code only.

//! \mainpage Crashpad Interface Documentation
//!
//! Most generated interface documentation is reachable through <a
//! href="namespaces.html">Namespaces</a>, <a href="annotated.html">Classes</a>
//! (includes `struct`s, `union`s, and interfaces), or <a
//! href="files.html">Files</a> (includes macros).
//!
//! Additional documentation is available at the <a
//! href="https://crashpad.chromium.org/">Crashpad home page</a>.
