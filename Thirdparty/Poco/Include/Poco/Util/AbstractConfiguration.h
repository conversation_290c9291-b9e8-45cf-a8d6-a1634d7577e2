//
// AbstractConfiguration.h
//
// Library: Util
// Package: Configuration
// Module:  AbstractConfiguration
//
// Definition of the AbstractConfiguration class.
//
// Copyright (c) 2004-2006, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#ifndef Util_AbstractConfiguration_INCLUDED
#define Util_AbstractConfiguration_INCLUDED


#include "Poco/Util/Util.h"
#include "Poco/Mutex.h"
#include "Poco/RefCountedObject.h"
#include "Poco/AutoPtr.h"
#include "Poco/BasicEvent.h"
#include <vector>
#include <utility>


namespace Poco {
namespace Util {


class Util_API AbstractConfiguration: public Poco::RefCountedObject
	/// AbstractConfiguration is an abstract base class for different
	/// kinds of configuration data, such as INI files, property files,
	/// XML configuration files or the Windows Registry.
	///
	/// Configuration property keys have a hierarchical format, consisting
	/// of names separated by periods. The exact interpretation of key names
	/// is up to the actual subclass implementation of AbstractConfiguration.
	/// Keys are case sensitive.
	///
	/// All public methods are synchronized, so the class is safe for multithreaded use.
	/// AbstractConfiguration implements reference counting based garbage collection.
	///
	/// Subclasses must override the getRaw(), setRaw() and enumerate() methods.
{
public:
	using Keys = std::vector<std::string>;
	using Ptr = AutoPtr<AbstractConfiguration>;

	class KeyValue
		/// A key-value pair, used as event argument.
	{
	public:
		KeyValue(const std::string& key, std::string& value):
			_key(key),
			_value(value)
		{
		}

		const std::string& key() const
		{
			return _key;
		}

		const std::string& value() const
		{
			return _value;
		}

		std::string& value()
		{
			return _value;
		}

	private:
		const std::string& _key;
		std::string& _value;
	};

	Poco::BasicEvent<KeyValue> propertyChanging;
		/// Fired before a property value is changed or
		/// a new property is created.
		///
		/// Can be used to check or fix a property value,
		/// or to cancel the change by throwing an exception.
		///
		/// The event delegate can use one of the get...() functions
		/// to obtain the current property value.

	Poco::BasicEvent<const KeyValue> propertyChanged;
		/// Fired after a property value has been changed
		/// or a property has been created.

	Poco::BasicEvent<const std::string> propertyRemoving;
		/// Fired before a property is removed by a
		/// call to remove().
		///
		/// Note: This will even be fired if the key
		/// does not exist and the remove operation will
		/// fail with an exception.

	Poco::BasicEvent<const std::string> propertyRemoved;
		/// Fired after a property has been removed by
		/// a call to remove().

	AbstractConfiguration();
		/// Creates the AbstractConfiguration.

	bool hasProperty(const std::string& key) const;
		/// Returns true iff the property with the given key exists.

	bool hasOption(const std::string& key) const;
		/// Returns true iff the property with the given key exists.
		///
		/// Same as hasProperty().

	bool has(const std::string& key) const;
		/// Returns true iff the property with the given key exists.
		///
		/// Same as hasProperty().

	std::string getString(const std::string& key) const;
		/// Returns the string value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	std::string getString(const std::string& key, const std::string& defaultValue) const;
		/// If a property with the given key exists, returns the property's string value,
		/// otherwise returns the given default value.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	std::string getRawString(const std::string& key) const;
		/// Returns the raw string value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// References to other properties are not expanded.

	std::string getRawString(const std::string& key, const std::string& defaultValue) const;
		/// If a property with the given key exists, returns the property's raw string value,
		/// otherwise returns the given default value.
		/// References to other properties are not expanded.

	int getInt(const std::string& key) const;
		/// Returns the int value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to an int.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	unsigned int getUInt(const std::string& key) const;
		/// Returns the unsigned int value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to an unsigned int.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	int getInt(const std::string& key, int defaultValue) const;
		/// If a property with the given key exists, returns the property's int value,
		/// otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to an int.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	unsigned int getUInt(const std::string& key, unsigned int defaultValue) const;
		/// If a property with the given key exists, returns the property's unsigned int
		/// value, otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to an unsigned int.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	Poco::Int32 getInt32(const std::string& key) const;
		/// Returns the 32-bit int value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to an Int32.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).
		
	Poco::UInt32 getUInt32(const std::string& key) const;
		/// Returns the 32-bit unsigned int value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to an UInt32.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).
		
	Poco::Int32 getInt32(const std::string& key, Poco::Int32 defaultValue) const;
		/// If a property with the given key exists, returns the property's 32-bit int value,
		/// otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to an Int32.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).
		
	Poco::UInt32 getUInt32(const std::string& key, Poco::UInt32 defaultValue) const;
		/// If a property with the given key exists, returns the property's 32-bit unsigned int
		/// value, otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to an UInt32.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	Poco::Int16 getInt16(const std::string& key) const;
		/// Returns the 16-bit int value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException or a RangeException if the property can not be converted
		/// to an Int16.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).
		
	Poco::UInt16 getUInt16(const std::string& key) const;
		/// Returns the unsigned 16-bit int value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException or a RangeException if the property can not be converted
		/// to an UInt16.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).
		
	Poco::Int16 getInt16(const std::string& key, Poco::Int16 defaultValue) const;
		/// If a property with the given key exists, returns the property's 16-bit int value,
		/// otherwise returns the given default value.
		/// Throws a SyntaxException or a RangeException if the property can not be converted
		/// to an Int16.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).
		
	Poco::UInt16 getUInt16(const std::string& key, Poco::UInt16 defaultValue) const;
		/// If a property with the given key exists, returns the property's unsigned 16-bit int
		/// value, otherwise returns the given default value.
		/// Throws a SyntaxException or a RangeException if the property can not be converted
		/// to an UInt16.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

#if defined(POCO_HAVE_INT64)

	Int64 getInt64(const std::string& key) const;
		/// Returns the Int64 value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to an Int64.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	UInt64 getUInt64(const std::string& key) const;
		/// Returns the UInt64 value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to an UInt64.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	Int64 getInt64(const std::string& key, Int64 defaultValue) const;
		/// If a property with the given key exists, returns the property's Int64 value,
		/// otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to an Int64.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	UInt64 getUInt64(const std::string& key, UInt64 defaultValue) const;
		/// If a property with the given key exists, returns the property's UInt64
		/// value, otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to an UInt64.
		/// Numbers starting with 0x are treated as hexadecimal.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

#endif // defined(POCO_HAVE_INT64)

	double getDouble(const std::string& key) const;
		/// Returns the double value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to a double.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	double getDouble(const std::string& key, double defaultValue) const;
		/// If a property with the given key exists, returns the property's double value,
		/// otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to an double.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	bool getBool(const std::string& key) const;
		/// Returns the boolean value of the property with the given name.
		/// Throws a NotFoundException if the key does not exist.
		/// Throws a SyntaxException if the property can not be converted
		/// to a boolean.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	bool getBool(const std::string& key, bool defaultValue) const;
		/// If a property with the given key exists, returns the property's boolean value,
		/// otherwise returns the given default value.
		/// Throws a SyntaxException if the property can not be converted
		/// to a boolean.
		/// The following string values can be converted into a boolean:
		///   - numerical values: non zero becomes true, zero becomes false
		///   - strings: true, yes, on become true, false, no, off become false
		/// Case does not matter.
		/// If the value contains references to other properties (${<property>}, or
		/// ${<property>:-<default>}), these are expanded (see expand()).

	virtual void setString(const std::string& key, const std::string& value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

	virtual void setInt(const std::string& key, int value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

	virtual void setUInt(const std::string& key, unsigned int value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

	virtual void setInt16(const std::string& key, Poco::Int16 value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.
		
	virtual void setUInt16(const std::string& key, Poco::UInt16 value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

	virtual void setInt32(const std::string& key, Poco::Int32 value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.
		
	virtual void setUInt32(const std::string& key, Poco::UInt32 value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

#if defined(POCO_HAVE_INT64)

	virtual void setInt64(const std::string& key, Int64 value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

	virtual void setUInt64(const std::string& key, UInt64 value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

#endif // defined(POCO_HAVE_INT64)

	virtual void setDouble(const std::string& key, double value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

	virtual void setBool(const std::string& key, bool value);
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.

	void keys(Keys& range) const;
		/// Returns in range the names of all keys at root level.

	void keys(const std::string& key, Keys& range) const;
		/// Returns in range the names of all subkeys under the given key.
		/// If an empty key is passed, all root level keys are returned.

	const Ptr createView(const std::string& prefix) const;
		/// Creates a non-mutable view (see ConfigurationView) into the configuration.

	Ptr createView(const std::string& prefix);
		/// Creates a view (see ConfigurationView) into the configuration.

	const Ptr createLocalView(const std::string& prefix) const;
		/// Creates a non-mutable view (see LocalConfigurationView) into the configuration.

	Ptr createLocalView(const std::string& prefix);
		/// Creates a view (see LocalConfigurationView) into the configuration.
	
	std::string expand(const std::string& value) const;
		/// Replaces all occurrences of ${<property>} in value with the
		/// value of the <property>. If <property> does not exist,
		/// nothing is changed.
		///
		/// It is also possible to specify a default value for a referenced
		/// property that does not exist, using ${<property>:-<default>}.
		/// Note that currently a default value cannot contain a closing
		/// curly bracket ("}"). The default value also cannot reference
		/// another variable.
		///
		/// If a circular property reference is detected, a
		/// CircularReferenceException will be thrown.

	void remove(const std::string& key);
		/// Removes the property with the given key.
		///
		/// Does nothing if the key does not exist.

	void enableEvents(bool enable = true);
		/// Enables (or disables) events.

	bool eventsEnabled() const;
		/// Returns true iff events are enabled.

protected:
	class ScopedLock
		/// A helper class allowing to temporarily
		/// lock an entire AbstractConfiguration,
		/// for use by subclasses. A typical use 
		/// case is loading or saving an entire
		/// configuration in a thread-safe way.
		///
		/// Caution: Thoughtless use of this class 
		/// may easily lead to deadlock situations
		/// in connection with events if any of the
		/// mutating methods (set...(), remove())
		/// are called with the lock held. Therefore
		/// this class is available to subclasses
		/// only, not for general use.
	{
	public:
		explicit ScopedLock(const AbstractConfiguration& c):
			_c(c)
		{
			_c._mutex.lock();
		}

		~ScopedLock()
		{
			_c._mutex.unlock();
		}

	private:
		const AbstractConfiguration& _c;
	};

	virtual bool getRaw(const std::string& key, std::string& value) const = 0;
		/// If the property with the given key exists, stores the property's value
		/// in value and returns true. Otherwise, returns false.
		///
		/// Must be overridden by subclasses.

	virtual void setRaw(const std::string& key, const std::string& value) = 0;
		/// Sets the property with the given key to the given value.
		/// An already existing value for the key is overwritten.
		///
		/// Must be overridden by subclasses.

	virtual void enumerate(const std::string& key, Keys& range) const = 0;
		/// Returns in range the names of all subkeys under the given key.
		/// If an empty key is passed, all root level keys are returned.

	virtual void removeRaw(const std::string& key);
		/// Removes the property with the given key.
		///
		/// Does nothing if the key does not exist.
		///
		/// Should be overridden by subclasses; the default
		/// implementation throws a Poco::NotImplementedException.

	static int parseInt(const std::string& value);
		/// Returns string as signed integer.
		/// Decimal and hexadecimal notation is supported.

	static unsigned parseUInt(const std::string& value);
		/// Returns string as unsigned integer.
		/// Decimal and hexadecimal notation is supported.

	static Poco::Int16 parseInt16(const std::string& value);
		/// Returns string as signed 16-bit integer.
		/// Decimal and hexadecimal notation is supported.

	static Poco::UInt16 parseUInt16(const std::string& value);
		/// Returns string as unsigned 16-bit integer.
		/// Decimal and hexadecimal notation is supported.

#if defined(POCO_HAVE_INT64)

	static Int64 parseInt64(const std::string& value);
		/// Returns string as 64-bit signed integer.
		/// Decimal and hexadecimal notation is supported.

	static UInt64 parseUInt64(const std::string& value);
		/// Returns string as 64-bit unsigned integer.
		/// Decimal and hexadecimal notation is supported.

#endif // defined(POCO_HAVE_INT64)

	static bool parseBool(const std::string& value);
	void setRawWithEvent(const std::string& key, std::string value);

	virtual ~AbstractConfiguration();

private:
	std::string internalExpand(const std::string& value) const;
	std::string uncheckedExpand(const std::string& value) const;

	AbstractConfiguration(const AbstractConfiguration&);
	AbstractConfiguration& operator = (const AbstractConfiguration&);

	mutable int _depth;
	bool        _eventsEnabled;
	mutable Poco::Mutex _mutex;

	friend class LayeredConfiguration;
	friend class ConfigurationView;
	friend class LocalConfigurationView;
	friend class ConfigurationMapper;
	friend class ScopedLock;
};


//
// inlines
//


inline Poco::Int32 AbstractConfiguration::getInt32(const std::string& key) const
{
	return getInt(key);
}

	
inline Poco::Int32 AbstractConfiguration::getInt32(const std::string& key, Poco::Int32 defaultValue) const
{
	return getInt(key, defaultValue);
}


inline Poco::UInt32 AbstractConfiguration::getUInt32(const std::string& key) const
{
	return getUInt(key);
}


inline Poco::UInt32 AbstractConfiguration::getUInt32(const std::string& key, Poco::UInt32 defaultValue) const
{
	return getUInt(key, defaultValue);
}


} } // namespace Poco::Util


#endif // Util_AbstractConfiguration_INCLUDED
