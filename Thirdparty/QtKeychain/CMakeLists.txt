project(QtKeychain LANGUAGES CXX)

# QtKeychain 静态库配置
set(QTKEYCHAIN_ROOT ${CMAKE_CURRENT_SOURCE_DIR})
set(QTKEYCHAIN_INCLUDE_DIR ${QTKEYCHAIN_ROOT}/include)
set(QTKEYCHAIN_LIB_DIR ${QTKEYCHAIN_ROOT}/lib)

# 创建QtKeychain接口库
add_library(QtKeychain INTERFACE)

# 设置头文件包含路径
target_include_directories(QtKeychain INTERFACE ${QTKEYCHAIN_INCLUDE_DIR})

if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(QTKEYCHAIN_LIB ${QTKEYCHAIN_LIB_DIR}/debug)
else()
    set(QTKEYCHAIN_LIB ${QTKEYCHAIN_LIB_DIR}/release)
endif()

# 链接库文件
target_link_libraries(QtKeychain
    INTERFACE
    ${QTKEYCHAIN_LIB}
    Qt6::Core
)

# 在Windows平台上添加必需的系统库
if(WIN32)
    target_link_libraries(QtKeychain
        INTERFACE
        crypt32     # Windows Cryptography API
        advapi32    # Advanced Windows API
    )
endif()

# 设置QtKeychain相关的预处理器定义
target_compile_definitions(QtKeychain
    INTERFACE
    QTKEYCHAIN_NO_EXPORT  # 使用静态库时不需要导出宏
)

