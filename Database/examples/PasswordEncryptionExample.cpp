#include "DatabaseManager.h"
#include <QCoreApplication>
#include <QDebug>
#include <QTimer>

/**
 * @brief 密码加密和安全存储示例
 * 
 * 演示如何使用DatabaseManager的新功能：
 * 1. 自动生成随机数据库密码
 * 2. 使用PocoCrypto对称加密密码
 * 3. 使用QtKeychain安全存储加密密钥
 */
class PasswordEncryptionExample : public QObject {
    Q_OBJECT

public:
    explicit PasswordEncryptionExample(QObject *parent = nullptr) : QObject(parent) {
        m_dbManager = new DatabaseManager();
    }

    ~PasswordEncryptionExample() {
        delete m_dbManager;
    }

    void runExample() {
        qDebug() << "=== 密码加密和安全存储示例 ===";
        
        // 示例1：手动生成和加密密码
        demonstrateManualPasswordEncryption();
        
        // 示例2：自动生成密码并初始化数据库
        QTimer::singleShot(2000, this, &PasswordEncryptionExample::demonstrateAutoPasswordGeneration);
        
        // 示例3：检索和解密密码
        QTimer::singleShot(4000, this, &PasswordEncryptionExample::demonstratePasswordRetrieval);
    }

private slots:
    void demonstrateManualPasswordEncryption() {
        qDebug() << "\n--- 示例1：手动密码加密 ---";
        
        // 生成随机密码
        QString password = DatabaseManager::generateRandomPassword(16);
        qDebug() << "生成的随机密码:" << password;
        
        // 生成加密密钥
        QString encryptionKey = DatabaseManager::generateRandomPassword(32);
        qDebug() << "生成的加密密钥:" << encryptionKey;
        
        // 加密密码
        QString encryptedPassword = DatabaseManager::encryptPassword(password, encryptionKey);
        qDebug() << "加密后的密码:" << encryptedPassword;
        
        // 解密密码验证
        QString decryptedPassword = DatabaseManager::decryptPassword(encryptedPassword, encryptionKey);
        qDebug() << "解密后的密码:" << decryptedPassword;
        qDebug() << "密码验证:" << (password == decryptedPassword ? "成功" : "失败");
        
        // 存储加密密钥到QtKeychain
        DatabaseManager::storeEncryptionKey("test_key", encryptionKey, 
            [](bool success, const QString &error) {
                if (success) {
                    qDebug() << "加密密钥存储成功";
                } else {
                    qDebug() << "加密密钥存储失败:" << error;
                }
            });
    }
    
    void demonstrateAutoPasswordGeneration() {
        qDebug() << "\n--- 示例2：自动密码生成和数据库初始化 ---";
        
        QString dbPath = "encrypted_example.db";
        
        // 使用自动密码生成初始化数据库
        bool result = m_dbManager->initialize(dbPath, true, 10, 
            [](const QString &generatedPassword) {
                qDebug() << "自动生成的数据库密码:" << generatedPassword;
                qDebug() << "密码已加密并安全存储";
            });
        
        if (result) {
            qDebug() << "数据库初始化请求已提交（异步处理中）";
        } else {
            qDebug() << "数据库初始化请求失败";
        }
    }
    
    void demonstratePasswordRetrieval() {
        qDebug() << "\n--- 示例3：密码检索和解密 ---";
        
        // 从QtKeychain检索加密密钥
        DatabaseManager::retrieveEncryptionKey("test_key", 
            [](bool success, const QString &key, const QString &error) {
                if (success) {
                    qDebug() << "成功检索加密密钥:" << key;
                    
                    // 这里可以使用检索到的密钥来解密存储的密码
                    // 在实际应用中，您会从某个地方获取加密的密码
                    qDebug() << "密钥检索成功，可以用于解密数据库密码";
                } else {
                    qDebug() << "密钥检索失败:" << error;
                }
            });
    }

private:
    DatabaseManager *m_dbManager;
};

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    PasswordEncryptionExample example;
    example.runExample();
    
    // 运行6秒后退出
    QTimer::singleShot(6000, &app, &QCoreApplication::quit);
    
    return app.exec();
}

#include "PasswordEncryptionExample.moc"
