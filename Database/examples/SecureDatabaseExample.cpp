#include "DatabaseManager.h"
#include "TestRepository.h"
#include "Test.h"
#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include <QFileInfo>

/**
 * @brief 安全数据库应用示例
 * 
 * 演示完整的安全数据库工作流程：
 * 1. 检查数据库是否存在
 * 2. 如果是新数据库，自动生成密码并加密存储
 * 3. 如果是现有数据库，检索密钥并解密密码
 * 4. 使用解密的密码连接数据库
 * 5. 执行数据库操作
 */
class SecureDatabaseExample : public QObject {
    Q_OBJECT

public:
    explicit SecureDatabaseExample(QObject *parent = nullptr) 
        : QObject(parent), m_dbManager(nullptr), m_repository(nullptr) {
    }

    ~SecureDatabaseExample() {
        delete m_repository;
        delete m_dbManager;
    }

    void runExample() {
        qDebug() << "=== 安全数据库应用示例 ===";
        
        QString dbPath = "secure_app_database.db";
        bool isNewDatabase = !QFileInfo(dbPath).exists();
        
        if (isNewDatabase) {
            qDebug() << "检测到新数据库，将自动生成安全密码";
            createNewSecureDatabase(dbPath);
        } else {
            qDebug() << "检测到现有数据库，将检索密码";
            connectToExistingDatabase(dbPath);
        }
    }

private slots:
    void createNewSecureDatabase(const QString &dbPath) {
        qDebug() << "\n--- 创建新的安全数据库 ---";
        
        m_dbManager = new DatabaseManager();
        
        // 自动生成密码并初始化数据库
        bool result = m_dbManager->initialize(dbPath, true, 10, 
            [this, dbPath](const QString &generatedPassword) {
                qDebug() << "数据库密码已生成并安全存储";
                qDebug() << "密码长度:" << generatedPassword.length() << "字符";
                
                // 延迟一下确保数据库完全初始化
                QTimer::singleShot(1000, this, [this]() {
                    setupDatabaseAndPerformOperations();
                });
            });
        
        if (result) {
            qDebug() << "新数据库初始化请求已提交";
        } else {
            qDebug() << "新数据库初始化失败";
        }
    }
    
    void connectToExistingDatabase(const QString &dbPath) {
        qDebug() << "\n--- 连接到现有安全数据库 ---";

        m_dbManager = new DatabaseManager();

        // 使用新的方法：自动检索存储的加密密码并解密
        m_dbManager->initializeWithStoredPassword(dbPath, 10,
            [this](bool success, const QString &error) {
                if (success) {
                    qDebug() << "成功连接到现有数据库（使用存储的加密密码）";
                    setupDatabaseAndPerformOperations();
                } else {
                    qDebug() << "连接到现有数据库失败:" << error;
                    qDebug() << "可能需要重新生成密码或数据库已损坏";
                }
            });
    }
    
    void setupDatabaseAndPerformOperations() {
        qDebug() << "\n--- 设置数据库并执行操作 ---";
        
        if (!m_dbManager || !m_dbManager->isConnected()) {
            qDebug() << "数据库未连接，无法继续";
            return;
        }
        
        // 创建表
        if (!m_dbManager->createTables()) {
            qDebug() << "创建表失败";
            return;
        }
        
        qDebug() << "数据库表创建成功";
        
        // 创建Repository并执行操作
        m_repository = new TestRepository(m_dbManager);
        
        // 连接信号
        connect(m_repository, &TestRepository::operationCompleted,
                this, [](const QString& operation, bool success) {
                    qDebug() << "操作完成:" << operation << "成功:" << success;
                });
        
        connect(m_repository, &TestRepository::errorOccurred,
                this, [](const QString& error) {
                    qDebug() << "操作错误:" << error;
                });
        
        // 执行一些数据库操作
        performDatabaseOperations();
    }
    
    void performDatabaseOperations() {
        qDebug() << "\n--- 执行数据库操作 ---";
        
        // 创建测试数据
        Test testData;
        testData.set("name", "安全数据库测试");
        testData.set("description", "使用加密密码的数据库操作");
        testData.set("created_at", QDateTime::currentDateTime().toString(Qt::ISODate).toStdString());
        
        // 异步创建记录
        m_repository->createTest(testData, [this](bool success, int id) {
            if (success) {
                qDebug() << "成功创建测试记录，ID:" << id;
                
                // 查询记录
                m_repository->getTestById(id, [](bool success, const Test &test) {
                    if (success) {
                        qDebug() << "成功查询记录:";
                        qDebug() << "  名称:" << QString::fromStdString(test.get("name"));
                        qDebug() << "  描述:" << QString::fromStdString(test.get("description"));
                        qDebug() << "  创建时间:" << QString::fromStdString(test.get("created_at"));
                    } else {
                        qDebug() << "查询记录失败";
                    }
                });
            } else {
                qDebug() << "创建测试记录失败";
            }
        });
        
        // 5秒后退出应用
        QTimer::singleShot(5000, qApp, &QCoreApplication::quit);
    }

private:
    DatabaseManager *m_dbManager;
    TestRepository *m_repository;
};

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    SecureDatabaseExample example;
    example.runExample();
    
    return app.exec();
}

#include "SecureDatabaseExample.moc"
