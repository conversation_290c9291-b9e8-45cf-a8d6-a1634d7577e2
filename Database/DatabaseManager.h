#ifndef DATABASEMANAGER_H
#define DATABASEMANAGER_H

#include <QList>
#include <memory>
#include <mutex>
#include <QThread>
#include <QMutex>
#include <QMutexLocker>
#include "Poco/Data/Session.h"
#include "Poco/Data/SessionPool.h"
#include <functional>

#include "sqlite3.h"


/**
 * @brief 数据库管理器类
 *
 * 使用POCO ORM管理SQLite数据库连接和操作
 * 支持线程安全操作和安全连接配置
 */
class DatabaseManager {
    friend class DatabaseTest;
    friend class PasswordEncryptionTest;

public:
    DatabaseManager();

    ~DatabaseManager();

    /**
     * @brief 初始化数据库连接
     * @param dbPath 数据库文件路径
     * @param maxConnections 最大连接数（用于连接池）
     * @return 成功返回true，失败返回false
     */
    bool initialize(const QString &dbPath, int maxConnections = 10);

    /**
     * @brief 初始化数据库连接（自动生成随机密码）
     * @param dbPath 数据库文件路径
     * @param autoGeneratePassword 是否自动生成随机密码
     * @param maxConnections 最大连接数（用于连接池）
     * @param onPasswordGenerated 密码生成完成回调（可选）
     * @return 成功返回true，失败返回false
     */
    bool initialize(const QString &dbPath, bool autoGeneratePassword, int maxConnections = 10,
                    std::function<void(const QString &)> onPasswordGenerated = nullptr);

    /**
     * @brief 创建所有数据表
     * @return 成功返回true，失败返回false
     */
    bool createTables();

    /**
     * @brief 执行SQL文件中的语句
     * @param sqlFilePath SQL文件路径
     * @return 成功返回true，失败返回false
     */
    bool executeSqlFile(const QString &sqlFilePath);

    /**
     * @brief 执行单条SQL语句
     * @param sql SQL语句
     * @return 成功返回true，失败返回false
     */
    bool executeSQL(const QString &sql);

    /**
     * @brief 获取数据库会话指针（线程安全）
     * @return 数据库会话指针，未初始化时返回nullptr
     */
    Poco::Data::Session *getSession();

    /**
     * @brief 获取线程安全的会话（从连接池）
     * @return 会话的智能指针
     */
    std::unique_ptr<Poco::Data::Session> getThreadSafeSession();

    /**
     * @brief 检查数据库连接是否有效
     * @return 有效返回true，无效返回false
     */
    bool isConnected() const;

    /**
     * @brief 设置数据库安全选项
     * @param enableWAL 启用WAL模式（提高并发性能）
     * @param enableForeignKeys 启用外键约束
     * @param busyTimeout 忙等待超时时间（毫秒）
     * @return 成功返回true，失败返回false
     */
    bool configureSecurity(bool enableWAL = true, bool enableForeignKeys = true, int busyTimeout = 30000);

    /**
     * @brief 获取当前线程ID（用于调试）
     * @return 当前线程ID
     */
    Qt::HANDLE getCurrentThreadId() const;

    /**
     * @brief 生成随机数据库密码
     * @param length 密码长度（默认32字符）
     * @return 生成的随机密码
     */
    static QString generateRandomPassword(int length = 32);

    /**
     * @brief 使用PocoCrypto加密密码
     * @param password 要加密的密码
     * @param encryptionKey 加密密钥
     * @return 加密后的密码（Base64编码）
     */
    static QString encryptPassword(const QString &password, const QString &encryptionKey);

    /**
     * @brief 使用PocoCrypto解密密码
     * @param encryptedPassword 加密的密码（Base64编码）
     * @param encryptionKey 解密密钥
     * @return 解密后的密码
     */
    static QString decryptPassword(const QString &encryptedPassword, const QString &encryptionKey);

    /**
     * @brief 使用QtKeychain存储加密密钥
     * @param keyName 密钥名称
     * @param key 要存储的密钥
     * @param onCompleted 完成回调
     */
    static void storeEncryptionKey(const QString &keyName, const QString &key,
                                   std::function<void(bool success, const QString &error)> onCompleted);

    /**
     * @brief 使用QtKeychain检索加密密钥
     * @param keyName 密钥名称
     * @param onCompleted 完成回调
     */
    static void retrieveEncryptionKey(const QString &keyName,
                                      std::function<void(bool success, const QString &key, const QString &error)>
                                      onCompleted);

    /**
     * @brief 存储加密的数据库密码到配置文件
     * @param dbPath 数据库路径
     * @param encryptedPassword 加密的密码
     * @return 成功返回true
     */
    static bool storeEncryptedPassword(const QString &dbPath, const QString &encryptedPassword);

    /**
     * @brief 从配置文件检索加密的数据库密码
     * @param dbPath 数据库路径
     * @return 加密的密码，失败返回空字符串
     */
    static QString retrieveEncryptedPassword(const QString &dbPath);

    /**
     * @brief 初始化数据库连接（使用存储的加密密码）
     * @param dbPath 数据库文件路径
     * @param maxConnections 最大连接数
     * @param onCompleted 完成回调
     */
    void initializeWithStoredPassword(const QString &dbPath, int maxConnections = 10,
                                      std::function<void(bool success, const QString &error)> onCompleted = nullptr);

    /**
     * @brief 完整的数据库初始化流程
     *
     * 该方法执行完整的数据库初始化流程：
     * 1. 尝试使用存储的密码连接数据库
     * 2. 如果没有存储密码，则生成新密码并初始化
     * 3. 创建数据库表结构
     * 4. 配置安全选项
     *
     * @param dbPath 数据库文件路径
     * @param maxConnections 最大连接数（默认10）
     * @param forceNewPassword 是否强制生成新密码（默认false）
     * @param onCompleted 完成回调函数，参数：(成功标志, 错误信息, 生成的密码)
     * @return 对于同步部分返回true，异步部分结果通过回调返回
     */
    bool initializeDatabase(const QString &dbPath,
                            int maxConnections = 10,
                            bool forceNewPassword = false,
                            std::function<void(bool success, const QString &error, const QString &generatedPassword)>
                            onCompleted = nullptr);

private:
    std::unique_ptr<Poco::Data::Session> m_session;
    std::unique_ptr<Poco::Data::SessionPool> m_sessionPool;
    QString m_dbPath;
    QString m_password;
    QString m_encryptionKey;
    bool m_initialized;
    bool m_passwordGenerated;
    mutable QMutex m_mutex; // 线程安全保护
    int m_maxConnections;

    /**
     * @brief 初始化POCO SQLite连接器
     */
    void initializeConnector();

    /**
     * @brief 构建连接字符串
     * @return 连接字符串
     */
    std::string buildConnectionString() const;

    /**
     * @brief 验证数据库文件权限
     * @return 权限验证通过返回true
     */
    bool validateFilePermissions() const;

    /**
     * @brief 生成数据库加密密钥
     * @return 生成的加密密钥
     */
    QString generateEncryptionKey();

    /**
     * @brief 设置数据库密码（内部使用）
     * @param password 数据库密码
     */
    void setDatabasePassword(const QString &password);

    /**
     * @brief 获取密钥存储名称
     * @return 密钥存储名称
     */
    QString getKeyStoreName() const;

    void execPRAGMA(sqlite3* dbHandle, const char* pragma, QString successMessage, QString failureMessage);
};

#endif // DATABASEMANAGER_H
