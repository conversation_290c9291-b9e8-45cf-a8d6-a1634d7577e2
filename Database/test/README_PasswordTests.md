# 密码加密功能测试文档

## 概述

本文档描述了DatabaseManager密码加密和安全存储功能的测试套件。

## 测试结构

### 1. 集成测试 (`Database/test/DatabaseManager/tst_database.cpp`)

在现有的数据库测试中添加了以下密码相关测试：

- `testGenerateRandomPassword()` - 随机密码生成测试
- `testPasswordEncryptionDecryption()` - 密码加密解密测试
- `testEncryptionKeyStorage()` - QtKeychain密钥存储测试
- `testEncryptedPasswordStorage()` - 配置文件密码存储测试
- `testAutoPasswordGeneration()` - 自动密码生成工作流程测试
- `testInitializeWithStoredPassword()` - 存储密码初始化测试
- `testCompletePasswordWorkflow()` - 完整工作流程测试

### 2. 专项测试 (`Database/test/PasswordEncryption/tst_password_encryption.cpp`)

专门针对密码加密功能的详细测试：

- **基础功能测试**
  - `testRandomPasswordGeneration()` - 随机密码生成
  - `testPasswordEncryptionDecryption()` - 加密解密功能
  - `testEncryptionKeyGeneration()` - 加密密钥生成

- **存储功能测试**
  - `testKeychainStorage()` - 系统密钥链存储
  - `testConfigFileStorage()` - 配置文件存储

- **集成测试**
  - `testAutoPasswordWorkflow()` - 自动密码工作流程
  - `testStoredPasswordWorkflow()` - 存储密码工作流程
  - `testPasswordChangeWorkflow()` - 密码更改工作流程

- **错误处理测试**
  - `testInvalidInputHandling()` - 无效输入处理
  - `testMissingDataHandling()` - 缺失数据处理

- **性能测试**
  - `testEncryptionPerformance()` - 加密性能测试

## 运行测试

### 方法1：使用批处理脚本

```bash
cd Database/test
run_password_tests.bat
```

### 方法2：使用CMake/CTest

```bash
cd build
ctest -R "Password" -V
```

### 方法3：直接运行可执行文件

```bash
# 运行集成测试
./build/Database/test/DatabaseManager/Debug/tst_database.exe

# 运行专项测试
./build/Database/test/PasswordEncryption/Debug/tst_password_encryption.exe
```

## 测试覆盖的功能

### 1. 随机密码生成
- 默认长度（32字符）密码生成
- 自定义长度密码生成
- 密码唯一性验证
- 字符集验证

### 2. 密码加密/解密
- AES-256-CBC加密算法
- 各种类型密码（简单、复杂、中文、长密码）
- 加密结果验证
- 解密正确性验证
- 错误密钥处理

### 3. 密钥存储（QtKeychain）
- 密钥存储到系统密钥链
- 密钥从系统密钥链检索
- 异步操作处理
- 错误情况处理

### 4. 密码存储（配置文件）
- 加密密码存储到INI文件
- 加密密码从INI文件检索
- 文件路径处理
- 权限验证

### 5. 完整工作流程
- 新数据库创建 + 自动密码生成
- 现有数据库连接 + 密码检索
- 密码更改流程
- 数据完整性验证

## 测试环境要求

### 系统要求
- Windows 10/11（支持Windows Credential Manager）
- 或 macOS（支持Keychain）
- 或 Linux（支持Secret Service API）

### 依赖库
- Qt6 Test框架
- PocoCrypto（AES加密）
- QtKeychain（系统密钥链访问）
- OpenSSL（底层加密支持）

### 权限要求
- 系统密钥链访问权限
- 临时文件创建权限
- 配置目录写入权限

## 测试数据

### 临时文件位置
- 测试数据库：`QTemporaryDir/test_*.db`
- 配置文件：`AppConfigLocation/database_passwords.ini`

### 密钥链条目
- 服务名：`TestFindGame`
- 密钥名格式：`db_key_[数据库名]`

## 故障排除

### 常见问题

1. **密钥链访问被拒绝**
   - 确保应用有访问系统密钥链的权限
   - 在macOS上可能需要用户授权

2. **配置文件权限错误**
   - 检查应用配置目录的写入权限
   - 确保临时目录可访问

3. **加密库初始化失败**
   - 验证OpenSSL库是否正确安装
   - 检查PocoCrypto库链接

4. **异步操作超时**
   - 增加测试超时时间
   - 检查系统密钥链服务状态

### 调试技巧

1. **启用详细输出**
   ```bash
   ./tst_password_encryption -v2
   ```

2. **运行特定测试**
   ```bash
   ./tst_password_encryption testPasswordEncryptionDecryption
   ```

3. **查看测试日志**
   - 测试输出包含详细的调试信息
   - 密码和密钥会在测试中显示（仅用于调试）

## 安全注意事项

### 测试环境安全
- 测试使用临时密钥和密码
- 测试完成后自动清理临时数据
- 不要在生产环境运行测试

### 密钥管理
- 测试密钥仅用于测试目的
- 实际应用中应使用更强的密钥生成策略
- 定期更换加密密钥

## 性能基准

### 预期性能指标
- 密码生成：< 10ms
- 加密/解密：< 50ms
- 密钥链操作：< 500ms
- 完整工作流程：< 2s

### 性能测试
专项测试中包含性能测试，验证各操作的执行时间是否在合理范围内。
